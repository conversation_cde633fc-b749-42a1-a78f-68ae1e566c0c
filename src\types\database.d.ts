import { RowDataPacket, OkPacket, ResultSetHeader } from 'mysql2';

// Define the QueryResult type to include array-like methods
export type QueryResult = RowDataPacket[] & {
  map<T>(callback: (value: RowDataPacket, index: number, array: RowDataPacket[]) => T): T[];
  find(predicate: (value: RowDataPacket, index: number, obj: RowDataPacket[]) => boolean): RowDataPacket | undefined;
  some(predicate: (value: RowDataPacket, index: number, array: RowDataPacket[]) => boolean): boolean;
  length: number;
  [index: number]: RowDataPacket & {
    count?: number;
  };
};

// Export the types from mysql2 for convenience
export { RowDataPacket, OkPacket, ResultSetHeader };
